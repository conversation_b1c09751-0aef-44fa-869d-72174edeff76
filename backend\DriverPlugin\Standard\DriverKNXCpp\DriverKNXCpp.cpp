#include "DriverKNXCpp.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <cstring>
#include <sstream>
#include <iomanip>

// KNX data type size mapping
static std::unordered_map<std::string, int> knxDataTypeMap_ = {
    {"bool", 1},
    {"uint8", 1},
    {"int8", 1},
    {"uint16", 2},
    {"int16", 2},
    {"uint32", 4},
    {"int32", 4},
    {"float16", 2},
    {"float32", 4}
};

namespace DRIVER
{
    CDriverKNXCpp::CDriverKNXCpp()
        : cb_(nullptr)
        , host_("localhost")
        , port_(6720)
        , socket_path_("/run/knx")
        , socket_fd_(-1)
        , connected_(false)
        , running_(false)
        , own_address_(0x0001)
    {
        std::cout << "CDriverKNXCpp constructor" << std::endl;
    }

    CDriverKNXCpp::~CDriverKNXCpp()
    {
        std::cout << "CDriverKNXCpp destructor" << std::endl;
        close(SProtocolNode{});
    }

    bool CDriverKNXCpp::open(const SProtocolNode& pn)
    {
        std::cout << "CDriverKNXCpp::open" << std::endl;
        
        SChannelProtocol* channelProtocol = static_cast<SChannelProtocol*>(pn.protocol);
        if (channelProtocol) {
            host_ = channelProtocol->host;
            port_ = channelProtocol->port;
            if (!channelProtocol->socket_path.empty()) {
                socket_path_ = channelProtocol->socket_path;
            }
        }

        // Initialize socket library on Windows
#ifdef _WIN32
        WSADATA wsaData;
        if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
            std::cerr << "WSAStartup failed" << std::endl;
            return false;
        }
#endif

        // Try to connect to knxd
        if (!connectToKNXD()) {
            std::cerr << "Failed to connect to knxd" << std::endl;
            return false;
        }

        // Start worker threads
        running_ = true;
        receive_thread_ = std::thread(&CDriverKNXCpp::receiveLoop, this);
        send_thread_ = std::thread(&CDriverKNXCpp::sendLoop, this);

        // Initialize iterator
        iter_ = groupAddrs_.begin();

        std::cout << "CDriverKNXCpp opened successfully" << std::endl;
        return true;
    }

    bool CDriverKNXCpp::close(const SProtocolNode& pn)
    {
        std::cout << "CDriverKNXCpp::close" << std::endl;
        
        // Stop worker threads
        running_ = false;
        send_cv_.notify_all();
        
        if (receive_thread_.joinable()) {
            receive_thread_.join();
        }
        if (send_thread_.joinable()) {
            send_thread_.join();
        }

        // Disconnect from knxd
        disconnectFromKNXD();

#ifdef _WIN32
        WSACleanup();
#endif

        return true;
    }

    bool CDriverKNXCpp::connectToKNXD()
    {
        std::lock_guard<std::mutex> lock(connection_mutex_);
        
        if (connected_) {
            return true;
        }

        // Try Unix socket first, then TCP
        if (!socket_path_.empty() && connectUnixSocket()) {
            connected_ = true;
            std::cout << "Connected to knxd via Unix socket: " << socket_path_ << std::endl;
            return true;
        }
        
        if (connectTCP()) {
            connected_ = true;
            std::cout << "Connected to knxd via TCP: " << host_ << ":" << port_ << std::endl;
            return true;
        }

        return false;
    }

    bool CDriverKNXCpp::connectTCP()
    {
        socket_fd_ = socket(AF_INET, SOCK_STREAM, 0);
        if (socket_fd_ < 0) {
            std::cerr << "Failed to create TCP socket" << std::endl;
            return false;
        }

        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(port_);
        
        if (inet_pton(AF_INET, host_.c_str(), &server_addr.sin_addr) <= 0) {
            std::cerr << "Invalid address: " << host_ << std::endl;
#ifdef _WIN32
            closesocket(socket_fd_);
#else
            ::close(socket_fd_);
#endif
            socket_fd_ = -1;
            return false;
        }

        if (connect(socket_fd_, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            std::cerr << "Failed to connect to " << host_ << ":" << port_ << std::endl;
#ifdef _WIN32
            closesocket(socket_fd_);
#else
            ::close(socket_fd_);
#endif
            socket_fd_ = -1;
            return false;
        }

        return true;
    }

    bool CDriverKNXCpp::connectUnixSocket()
    {
#ifdef _WIN32
        // Windows doesn't support Unix sockets in the same way
        return false;
#else
        socket_fd_ = socket(AF_UNIX, SOCK_STREAM, 0);
        if (socket_fd_ < 0) {
            std::cerr << "Failed to create Unix socket" << std::endl;
            return false;
        }

        struct sockaddr_un server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sun_family = AF_UNIX;
        strncpy(server_addr.sun_path, socket_path_.c_str(), sizeof(server_addr.sun_path) - 1);

        if (connect(socket_fd_, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            std::cerr << "Failed to connect to Unix socket: " << socket_path_ << std::endl;
            ::close(socket_fd_);
            socket_fd_ = -1;
            return false;
        }

        return true;
#endif
    }

    void CDriverKNXCpp::disconnectFromKNXD()
    {
        std::lock_guard<std::mutex> lock(connection_mutex_);
        
        if (socket_fd_ >= 0) {
#ifdef _WIN32
            closesocket(socket_fd_);
#else
            ::close(socket_fd_);
#endif
            socket_fd_ = -1;
        }
        connected_ = false;
    }

    void CDriverKNXCpp::receiveLoop()
    {
        std::cout << "Receive loop started" << std::endl;
        
        uint8_t buffer[1024];
        while (running_) {
            if (!connected_ || socket_fd_ < 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                continue;
            }

            fd_set readfds;
            FD_ZERO(&readfds);
            FD_SET(socket_fd_, &readfds);
            
            struct timeval timeout;
            timeout.tv_sec = 0;
            timeout.tv_usec = 100000; // 100ms timeout

            int result = select(socket_fd_ + 1, &readfds, nullptr, nullptr, &timeout);
            if (result > 0 && FD_ISSET(socket_fd_, &readfds)) {
                ssize_t bytes_received = recv(socket_fd_, (char*)buffer, sizeof(buffer), 0);
                if (bytes_received > 0) {
                    processReceivedData(buffer, bytes_received);
                } else if (bytes_received == 0) {
                    std::cout << "Connection closed by knxd" << std::endl;
                    connected_ = false;
                    break;
                } else {
                    std::cerr << "Receive error" << std::endl;
                    connected_ = false;
                    break;
                }
            } else if (result < 0) {
                std::cerr << "Select error" << std::endl;
                break;
            }
        }
        
        std::cout << "Receive loop ended" << std::endl;
    }

    void CDriverKNXCpp::sendLoop()
    {
        std::cout << "Send loop started" << std::endl;
        
        while (running_) {
            std::unique_lock<std::mutex> lock(send_mutex_);
            send_cv_.wait(lock, [this] { return !send_queue_.empty() || !running_; });
            
            if (!running_) {
                break;
            }
            
            while (!send_queue_.empty() && connected_) {
                KNXPacket packet = send_queue_.front();
                send_queue_.pop();
                lock.unlock();
                
                sendPacket(packet);
                
                lock.lock();
            }
        }
        
        std::cout << "Send loop ended" << std::endl;
    }

    void CDriverKNXCpp::setCallback(IDriverCallback* cb)
    {
        cb_ = cb;
    }

    EStatusCode CDriverKNXCpp::poll(const SProtocolNode& pn)
    {
        if (iter_ == groupAddrs_.end()) {
            iter_ = groupAddrs_.begin();
        }
        if (iter_ == groupAddrs_.end()) {
            return EStatusCode::eStatusSuccess;
        }

        // Create read request packet
        KNXPacket packet;
        packet.dest_addr = parseGroupAddress(iter_->first);
        packet.command = KNXCommand::GroupRead;
        packet.data_len = 0;
        packet.source_addr = own_address_;

        // Queue packet for sending
        {
            std::lock_guard<std::mutex> lock(send_mutex_);
            send_queue_.push(packet);
        }
        send_cv_.notify_one();

        iter_++;
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        return EStatusCode::eStatusDoNotSleep;
    }

    EStatusCode CDriverKNXCpp::control(const IProtocol* const channelProtocol,
                                     const IProtocol* const deviceProtocol,
                                     const IProtocol* const pointProtocol,
                                     const SControlInfo& controlInfo)
    {
        const SPointProtocol* const pp = static_cast<const SPointProtocol* const>(pointProtocol);
        if (!pp) {
            return EStatusCode::eStatusFail;
        }

        KNXPacket packet;
        packet.dest_addr = parseGroupAddress(pp->groupAddrWrite);
        packet.command = KNXCommand::GroupWrite;
        packet.source_addr = own_address_;

        // Convert value to KNX data format
        if (!convertToKNXData(controlInfo.value, pp->type, packet.data, packet.data_len)) {
            std::cerr << "Failed to convert value to KNX data format" << std::endl;
            return EStatusCode::eStatusFail;
        }

        // Queue packet for sending
        {
            std::lock_guard<std::mutex> lock(send_mutex_);
            send_queue_.push(packet);
        }
        send_cv_.notify_one();

        return EStatusCode::eStatusSuccess;
    }

    EStatusCode CDriverKNXCpp::controlSet(const std::unordered_map<std::string, std::vector<SControlSetInfo>>& controlValues)
    {
        // Not implemented for this driver
        return EStatusCode::eStatusSuccess;
    }

    std::string CDriverKNXCpp::getPointKey(const IProtocol* const channelProtocol,
                                         const IProtocol* const deviceProtocol,
                                         const IProtocol* const pointProtocol)
    {
        const SPointProtocol* const protocol = static_cast<const SPointProtocol* const>(pointProtocol);
        return protocol ? protocol->toString() : "";
    }

    IProtocol* CDriverKNXCpp::createProtocol(EProtocolType type, OriginProtocol& originProtocol)
    {
        switch (type)
        {
        case EProtocolType::eProtocolLink:
        {
            SChannelProtocol* protocol = new SChannelProtocol;
            protocol->host = originProtocol["Host"];
            if (originProtocol.find("Port") != originProtocol.end()) {
                protocol->port = std::stoi(originProtocol["Port"]);
            }
            if (originProtocol.find("SocketPath") != originProtocol.end()) {
                protocol->socket_path = originProtocol["SocketPath"];
            }
            return protocol;
        }
        case EProtocolType::eProtocolDevice:
        {
            return nullptr;
        }
        case EProtocolType::eProtocolPoint:
        {
            SPointProtocol* protocol = new SPointProtocol;
            protocol->groupAddrRead = originProtocol["GroupAddrRead"];
            protocol->groupAddrWrite = originProtocol["GroupAddrWrite"];
            protocol->type = originProtocol["Type"];

            if (knxDataTypeMap_.find(protocol->type) == knxDataTypeMap_.end()) {
                delete protocol;
                return nullptr;
            }

            groupAddrs_[protocol->groupAddrRead] = protocol;
            return protocol;
        }
        default:
            break;
        }
        return nullptr;
    }

    void CDriverKNXCpp::destoryProtocol(EProtocolType type, IProtocol* protocol)
    {
        if (protocol) {
            delete protocol;
        }
    }

    void CDriverKNXCpp::setFlag(EFlag flag)
    {
        // Not implemented for this driver
    }

    // Utility functions implementation
    uint16_t CDriverKNXCpp::parseGroupAddress(const std::string& addr)
    {
        // Parse group address in format "x/y/z" or "x.y.z"
        std::string delimiter = (addr.find('/') != std::string::npos) ? "/" : ".";

        size_t pos1 = addr.find(delimiter);
        size_t pos2 = addr.find(delimiter, pos1 + 1);

        if (pos1 == std::string::npos || pos2 == std::string::npos) {
            return 0;
        }

        int main_group = std::stoi(addr.substr(0, pos1));
        int middle_group = std::stoi(addr.substr(pos1 + 1, pos2 - pos1 - 1));
        int sub_group = std::stoi(addr.substr(pos2 + 1));

        return ((main_group & 0x1F) << 11) | ((middle_group & 0x07) << 8) | (sub_group & 0xFF);
    }

    std::string CDriverKNXCpp::formatGroupAddress(uint16_t addr)
    {
        int main_group = (addr >> 11) & 0x1F;
        int middle_group = (addr >> 8) & 0x07;
        int sub_group = addr & 0xFF;

        return std::to_string(main_group) + "/" +
               std::to_string(middle_group) + "/" +
               std::to_string(sub_group);
    }

    bool CDriverKNXCpp::sendPacket(const KNXPacket& packet)
    {
        if (!connected_ || socket_fd_ < 0) {
            return false;
        }

        EIBPacket eib_packet;
        encodeEIBPacket(packet, eib_packet);

        return sendEIBPacket(eib_packet);
    }

    bool CDriverKNXCpp::sendEIBPacket(const EIBPacket& packet)
    {
        std::lock_guard<std::mutex> lock(connection_mutex_);

        if (!connected_ || socket_fd_ < 0) {
            return false;
        }

        // Calculate total packet size
        uint16_t packet_size = (packet.header[0] << 8) | packet.header[1];

        // Send header first
        ssize_t sent = send(socket_fd_, (const char*)packet.header, 2, 0);
        if (sent != 2) {
            std::cerr << "Failed to send packet header" << std::endl;
            return false;
        }

        // Send data
        if (packet_size > 2) {
            sent = send(socket_fd_, (const char*)packet.data, packet_size - 2, 0);
            if (sent != (packet_size - 2)) {
                std::cerr << "Failed to send packet data" << std::endl;
                return false;
            }
        }

        logPacket(KNXPacket{}, true); // Log outgoing packet
        return true;
    }

    void CDriverKNXCpp::processReceivedData(const uint8_t* data, size_t length)
    {
        if (length < 2) {
            return;
        }

        // Parse EIB packet header
        EIBPacket eib_packet;
        uint16_t packet_size = (data[0] << 8) | data[1];

        if (packet_size > length || packet_size > sizeof(eib_packet.data) + 2) {
            std::cerr << "Invalid packet size: " << packet_size << std::endl;
            return;
        }

        eib_packet.header[0] = data[0];
        eib_packet.header[1] = data[1];

        if (packet_size > 2) {
            memcpy(eib_packet.data, data + 2, packet_size - 2);
        }

        // Decode to KNX packet
        KNXPacket knx_packet;
        if (decodeEIBPacket(eib_packet, knx_packet)) {
            handleKNXPacket(knx_packet);
        }
    }

    void CDriverKNXCpp::handleKNXPacket(const KNXPacket& packet)
    {
        std::string addr_str = formatGroupAddress(packet.dest_addr);

        auto info = groupAddrs_.find(addr_str);
        if (info == groupAddrs_.end()) {
            return;
        }

        if (packet.command == KNXCommand::GroupResponse || packet.command == KNXCommand::GroupWrite) {
            SData sdata;
            if (convertFromKNXData(packet.data, packet.data_len, info->second->type, sdata)) {
                cb_->onData(info->second->toString(), sdata);
                logPacket(packet, false); // Log incoming packet
            }
        }
    }

    bool CDriverKNXCpp::convertToKNXData(const SData& value, const std::string& type, uint8_t* data, uint8_t& length)
    {
        if (type == "bool") {
            KNXDataTypes::bool_to_data(value.toBool(), data, length);
        }
        else if (type == "uint8") {
            KNXDataTypes::uint8_to_data(value.toInt<uint8_t>(), data, length);
        }
        else if (type == "int8") {
            KNXDataTypes::int8_to_data(value.toInt<int8_t>(), data, length);
        }
        else if (type == "uint16") {
            KNXDataTypes::uint16_to_data(value.toInt<uint16_t>(), data, length);
        }
        else if (type == "int16") {
            KNXDataTypes::int16_to_data(value.toInt<int16_t>(), data, length);
        }
        else if (type == "uint32") {
            KNXDataTypes::uint32_to_data(value.toInt<uint32_t>(), data, length);
        }
        else if (type == "int32") {
            KNXDataTypes::int32_to_data(value.toInt<int32_t>(), data, length);
        }
        else if (type == "float16") {
            KNXDataTypes::float16_to_data(value.toFloat<float>(), data, length);
        }
        else if (type == "float32") {
            KNXDataTypes::float32_to_data(value.toFloat<float>(), data, length);
        }
        else {
            return false;
        }
        return true;
    }

    bool CDriverKNXCpp::convertFromKNXData(const uint8_t* data, uint8_t length, const std::string& type, SData& value)
    {
        if (type == "bool") {
            value.value = KNXDataTypes::data_to_bool(data, length);
        }
        else if (type == "uint8") {
            value.value = KNXDataTypes::data_to_uint8(data, length);
        }
        else if (type == "int8") {
            value.value = KNXDataTypes::data_to_int8(data, length);
        }
        else if (type == "uint16") {
            value.value = KNXDataTypes::data_to_uint16(data, length);
        }
        else if (type == "int16") {
            value.value = KNXDataTypes::data_to_int16(data, length);
        }
        else if (type == "uint32") {
            value.value = KNXDataTypes::data_to_uint32(data, length);
        }
        else if (type == "int32") {
            value.value = KNXDataTypes::data_to_int32(data, length);
        }
        else if (type == "float16") {
            value.value = KNXDataTypes::data_to_float16(data, length);
        }
        else if (type == "float32") {
            value.value = KNXDataTypes::data_to_float32(data, length);
        }
        else {
            return false;
        }
        return true;
    }

    void CDriverKNXCpp::encodeEIBPacket(const KNXPacket& knx_packet, EIBPacket& eib_packet)
    {
        // Simplified EIB packet encoding
        // This is a basic implementation - real EIB protocol is more complex

        uint8_t* data = eib_packet.data;
        size_t offset = 0;

        // EIB command (simplified)
        data[offset++] = 0x00; // EIB_GROUP_PACKET
        data[offset++] = static_cast<uint8_t>(knx_packet.command);

        // Source address
        data[offset++] = (knx_packet.source_addr >> 8) & 0xFF;
        data[offset++] = knx_packet.source_addr & 0xFF;

        // Destination address
        data[offset++] = (knx_packet.dest_addr >> 8) & 0xFF;
        data[offset++] = knx_packet.dest_addr & 0xFF;

        // Data length
        data[offset++] = knx_packet.data_len;

        // Data
        if (knx_packet.data_len > 0) {
            memcpy(data + offset, knx_packet.data, knx_packet.data_len);
            offset += knx_packet.data_len;
        }

        // Set packet size in header
        uint16_t total_size = offset + 2; // +2 for header
        eib_packet.header[0] = (total_size >> 8) & 0xFF;
        eib_packet.header[1] = total_size & 0xFF;
    }

    bool CDriverKNXCpp::decodeEIBPacket(const EIBPacket& eib_packet, KNXPacket& knx_packet)
    {
        // Simplified EIB packet decoding
        uint16_t packet_size = (eib_packet.header[0] << 8) | eib_packet.header[1];

        if (packet_size < 9) { // Minimum packet size
            return false;
        }

        const uint8_t* data = eib_packet.data;
        size_t offset = 0;

        // Skip EIB command
        offset += 2;

        // Source address
        knx_packet.source_addr = (data[offset] << 8) | data[offset + 1];
        offset += 2;

        // Destination address
        knx_packet.dest_addr = (data[offset] << 8) | data[offset + 1];
        offset += 2;

        // Data length
        knx_packet.data_len = data[offset++];

        if (knx_packet.data_len > sizeof(knx_packet.data)) {
            return false;
        }

        // Data
        if (knx_packet.data_len > 0) {
            memcpy(knx_packet.data, data + offset, knx_packet.data_len);
        }

        // Command (simplified)
        knx_packet.command = static_cast<KNXCommand>(data[1]);

        return true;
    }

    void CDriverKNXCpp::logPacket(const KNXPacket& packet, bool outgoing)
    {
        std::cout << (outgoing ? "TX: " : "RX: ")
                  << formatGroupAddress(packet.dest_addr)
                  << " [" << static_cast<int>(packet.data_len) << " bytes]";

        for (int i = 0; i < packet.data_len; ++i) {
            std::cout << " " << std::hex << std::setw(2) << std::setfill('0')
                      << static_cast<int>(packet.data[i]);
        }
        std::cout << std::dec << std::endl;
    }

    bool CDriverKNXCpp::isValidGroupAddress(const std::string& addr)
    {
        return parseGroupAddress(addr) != 0;
    }

    // KNX Data Types implementation
    namespace KNXDataTypes {

        bool data_to_bool(const uint8_t* data, uint8_t length)
        {
            if (length < 1) return false;
            return (data[0] & 0x01) == 1;
        }

        void bool_to_data(bool value, uint8_t* data, uint8_t& length)
        {
            data[0] = value ? 1 : 0;
            length = 1;
        }

        int8_t data_to_int8(const uint8_t* data, uint8_t length)
        {
            if (length < 1) return 0;
            return static_cast<int8_t>(data[0]);
        }

        void int8_to_data(int8_t value, uint8_t* data, uint8_t& length)
        {
            data[0] = static_cast<uint8_t>(value);
            length = 1;
        }

        uint8_t data_to_uint8(const uint8_t* data, uint8_t length)
        {
            if (length < 1) return 0;
            return data[0];
        }

        void uint8_to_data(uint8_t value, uint8_t* data, uint8_t& length)
        {
            data[0] = value;
            length = 1;
        }

        int16_t data_to_int16(const uint8_t* data, uint8_t length)
        {
            if (length < 2) return 0;
            return static_cast<int16_t>((data[0] << 8) | data[1]);
        }

        void int16_to_data(int16_t value, uint8_t* data, uint8_t& length)
        {
            data[0] = static_cast<uint8_t>(value >> 8);
            data[1] = static_cast<uint8_t>(value & 0xFF);
            length = 2;
        }

        uint16_t data_to_uint16(const uint8_t* data, uint8_t length)
        {
            if (length < 2) return 0;
            return static_cast<uint16_t>((data[0] << 8) | data[1]);
        }

        void uint16_to_data(uint16_t value, uint8_t* data, uint8_t& length)
        {
            data[0] = static_cast<uint8_t>(value >> 8);
            data[1] = static_cast<uint8_t>(value & 0xFF);
            length = 2;
        }

        int32_t data_to_int32(const uint8_t* data, uint8_t length)
        {
            if (length < 4) return 0;
            return static_cast<int32_t>((data[0] << 24) | (data[1] << 16) | (data[2] << 8) | data[3]);
        }

        void int32_to_data(int32_t value, uint8_t* data, uint8_t& length)
        {
            data[0] = static_cast<uint8_t>(value >> 24);
            data[1] = static_cast<uint8_t>(value >> 16);
            data[2] = static_cast<uint8_t>(value >> 8);
            data[3] = static_cast<uint8_t>(value & 0xFF);
            length = 4;
        }

        uint32_t data_to_uint32(const uint8_t* data, uint8_t length)
        {
            if (length < 4) return 0;
            return static_cast<uint32_t>((data[0] << 24) | (data[1] << 16) | (data[2] << 8) | data[3]);
        }

        void uint32_to_data(uint32_t value, uint8_t* data, uint8_t& length)
        {
            data[0] = static_cast<uint8_t>(value >> 24);
            data[1] = static_cast<uint8_t>(value >> 16);
            data[2] = static_cast<uint8_t>(value >> 8);
            data[3] = static_cast<uint8_t>(value & 0xFF);
            length = 4;
        }

        float data_to_float16(const uint8_t* data, uint8_t length)
        {
            if (length < 2) return 0.0f;

            // KNX DPT 9.xxx (2-byte float)
            uint8_t sign = (data[0] & 0x80) >> 7;
            uint8_t expo = (data[0] & 0x78) >> 3;
            uint16_t mant = ((data[0] & 0x07) << 8) | data[1];

            if (sign) {
                mant = (~mant + 1) & 0x07FF;
            }

            float result = 0.01f * mant * (1 << expo);
            return sign ? -result : result;
        }

        void float16_to_data(float value, uint8_t* data, uint8_t& length)
        {
            // KNX DPT 9.xxx (2-byte float) encoding
            bool sign = value < 0;
            if (sign) value = -value;

            // Find appropriate exponent
            uint8_t expo = 0;
            while (value >= 20.48f && expo < 15) {
                value /= 2;
                expo++;
            }

            uint16_t mant = static_cast<uint16_t>(value * 100);
            if (mant > 2047) mant = 2047;

            if (sign) {
                mant = (~mant + 1) & 0x07FF;
            }

            data[0] = (sign << 7) | (expo << 3) | ((mant >> 8) & 0x07);
            data[1] = mant & 0xFF;
            length = 2;
        }

        float data_to_float32(const uint8_t* data, uint8_t length)
        {
            if (length < 4) return 0.0f;

            union {
                float f;
                uint8_t b[4];
            } converter;

            // KNX uses big-endian format
            converter.b[3] = data[0];
            converter.b[2] = data[1];
            converter.b[1] = data[2];
            converter.b[0] = data[3];

            return converter.f;
        }

        void float32_to_data(float value, uint8_t* data, uint8_t& length)
        {
            union {
                float f;
                uint8_t b[4];
            } converter;

            converter.f = value;

            // Convert to KNX big-endian format
            data[0] = converter.b[3];
            data[1] = converter.b[2];
            data[2] = converter.b[1];
            data[3] = converter.b[0];
            length = 4;
        }
    }
}

// Export functions for driver factory
extern "C"
{
    DRIVER_API DRIVER::IDriver* createDriver()
    {
        return new DRIVER::CDriverKNXCpp;
    }

    DRIVER_API void destoryDriver(DRIVER::IDriver* p)
    {
        if (p) {
            delete p;
        }
    }
}
