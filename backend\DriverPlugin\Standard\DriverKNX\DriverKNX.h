#pragma once
#include "IDriver.h"

#include <iostream>
#include <unordered_map>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <queue>

// KNX/EIB client library headers
extern "C" {
#include <sys/socket.h>
#include <sys/un.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
}

// KNX protocol constants
enum KNXGroupCommand {
	GroupRead = 0,
	GroupResponse,
	GroupWrite
};

// KNX packet structure
struct KNXPacket {
	uint16_t dest_addr;
	uint8_t data[16];
	uint8_t data_len;
	KNXGroupCommand command;
};

namespace DRIVER
{
	struct SChannelProtocol : public IProtocol
	{
		std::string host;
	};

	struct SPointProtocol : public IProtocol
	{
		std::string groupAddrRead;
		std::string groupAddrWrite;
		std::string type;
		std::string toString() const
		{
			std::string key;
			key.append(groupAddrRead).append(".")
				.append(groupAddrWrite).append(".")
				.append(type);
			return key;
		}
	};

	class CDriverKNX : public IDriver
	{
	private:
		IDriverCallback* cb_;

		// KNX connection management
		std::string knxd_host_;
		int knxd_port_;
		int socket_fd_;
		bool connected_;

		// Threading for async operations
		std::thread receive_thread_;
		std::atomic<bool> running_;
		std::mutex mutex_;
		std::condition_variable cv_;

		// Packet queue for outgoing messages
		std::queue<KNXPacket> send_queue_;
		std::mutex send_mutex_;

		// Group address management
		std::unordered_map<std::string, SPointProtocol*> groupAddrs_;
		std::unordered_map<std::string, SPointProtocol*>::iterator iter_;

	private:
		// Internal methods
		bool connectToKNXD();
		void disconnectFromKNXD();
		void receiveLoop();
		bool sendPacket(const KNXPacket& packet);
		void processReceivedData(const uint8_t* data, size_t length);
		uint16_t parseGroupAddress(const std::string& addr);
		std::string formatGroupAddress(uint16_t addr);

	public:
		CDriverKNX();
		virtual ~CDriverKNX();

		//IDriver
		virtual bool open(const SProtocolNode& pn) override;
		virtual bool close(const SProtocolNode& pn) override;
		virtual EStatusCode control(const IProtocol* const channelProtocol, const IProtocol* const deviceProtocol, const IProtocol* const pointProtocol, const SControlInfo& controlInfo) override;
		virtual EStatusCode controlSet(const std::unordered_map<std::string, std::vector<SControlSetInfo>>& controlValues) override;
		virtual void setCallback(IDriverCallback* cb) override;
		virtual EStatusCode poll(const SProtocolNode& pn) override;
		virtual std::string getPointKey(const IProtocol* const channelProtocol, const IProtocol* const deviceProtocol, const IProtocol* const pointProtocol) override;
		virtual IProtocol* createProtocol(EProtocolType type, OriginProtocol& originProtocol) override;
		virtual void destoryProtocol(EProtocolType type, IProtocol* protocol) override;
		virtual void setFlag(EFlag flag) override;
	};

} //namespace DRIVER end

