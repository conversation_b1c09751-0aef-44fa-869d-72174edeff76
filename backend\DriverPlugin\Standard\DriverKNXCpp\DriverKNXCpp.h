#pragma once
#include "IDriver.h"

#include <iostream>
#include <unordered_map>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <memory>
#include <chrono>

// Socket headers
#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
typedef int socklen_t;
#define close closesocket
#else
#include <sys/socket.h>
#include <sys/un.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#endif

// KNX protocol constants
enum class KNXCommand : uint8_t {
    GroupRead = 0x00,
    GroupResponse = 0x40,
    GroupWrite = 0x80
};

enum class KNXPriority : uint8_t {
    System = 0x00,
    Normal = 0x01,
    Urgent = 0x02,
    Low = 0x03
};

// KNX packet structure
struct KNXPacket {
    uint16_t source_addr;
    uint16_t dest_addr;
    uint8_t data[16];
    uint8_t data_len;
    KNXCommand command;
    KNXPriority priority;
    bool repeat;
    
    KNXPacket() : source_addr(0), dest_addr(0), data_len(0), 
                  command(KNXCommand::GroupRead), priority(KNXPriority::Normal), repeat(false) {
        memset(data, 0, sizeof(data));
    }
};

// EIB/KNX client protocol structures
struct EIBPacket {
    uint8_t header[2];  // Packet length
    uint8_t data[255];  // Actual packet data
    
    EIBPacket() {
        memset(header, 0, sizeof(header));
        memset(data, 0, sizeof(data));
    }
};

namespace DRIVER
{
    struct SChannelProtocol : public IProtocol
    {
        std::string host;
        int port;
        std::string socket_path;  // For Unix socket connection
        
        SChannelProtocol() : port(6720) {}  // Default knxd port
    };

    struct SPointProtocol : public IProtocol
    {
        std::string groupAddrRead;
        std::string groupAddrWrite;
        std::string type;
        
        std::string toString() const
        {
            std::string key;
            key.append(groupAddrRead).append(".")
                .append(groupAddrWrite).append(".")
                .append(type);
            return key;
        }
    };

    class CDriverKNXCpp : public IDriver
    {
    private:
        IDriverCallback* cb_;
        
        // Connection parameters
        std::string host_;
        int port_;
        std::string socket_path_;
        int socket_fd_;
        bool connected_;
        
        // Threading for async operations
        std::thread receive_thread_;
        std::thread send_thread_;
        std::atomic<bool> running_;
        std::mutex connection_mutex_;
        
        // Packet queues
        std::queue<KNXPacket> send_queue_;
        std::mutex send_mutex_;
        std::condition_variable send_cv_;
        
        // Group address management
        std::unordered_map<std::string, SPointProtocol*> groupAddrs_;
        std::unordered_map<std::string, SPointProtocol*>::iterator iter_;
        
        // KNX address management
        uint16_t own_address_;
        
    private:
        // Connection management
        bool connectToKNXD();
        bool connectTCP();
        bool connectUnixSocket();
        void disconnectFromKNXD();
        
        // Threading functions
        void receiveLoop();
        void sendLoop();
        
        // Packet handling
        bool sendPacket(const KNXPacket& packet);
        bool sendEIBPacket(const EIBPacket& packet);
        void processReceivedData(const uint8_t* data, size_t length);
        void handleKNXPacket(const KNXPacket& packet);
        
        // Address conversion utilities
        uint16_t parseGroupAddress(const std::string& addr);
        std::string formatGroupAddress(uint16_t addr);
        
        // Data type conversion utilities
        bool convertToKNXData(const SData& value, const std::string& type, uint8_t* data, uint8_t& length);
        bool convertFromKNXData(const uint8_t* data, uint8_t length, const std::string& type, SData& value);
        
        // EIB protocol utilities
        void encodeEIBPacket(const KNXPacket& knx_packet, EIBPacket& eib_packet);
        bool decodeEIBPacket(const EIBPacket& eib_packet, KNXPacket& knx_packet);
        
        // Utility functions
        void logPacket(const KNXPacket& packet, bool outgoing = true);
        bool isValidGroupAddress(const std::string& addr);

    public:
        CDriverKNXCpp();
        virtual ~CDriverKNXCpp();

        // IDriver interface
        virtual bool open(const SProtocolNode& pn) override;
        virtual bool close(const SProtocolNode& pn) override;
        virtual EStatusCode control(const IProtocol* const channelProtocol, 
                                  const IProtocol* const deviceProtocol, 
                                  const IProtocol* const pointProtocol, 
                                  const SControlInfo& controlInfo) override;
        virtual EStatusCode controlSet(const std::unordered_map<std::string, std::vector<SControlSetInfo>>& controlValues) override;
        virtual void setCallback(IDriverCallback* cb) override;
        virtual EStatusCode poll(const SProtocolNode& pn) override;
        virtual std::string getPointKey(const IProtocol* const channelProtocol, 
                                      const IProtocol* const deviceProtocol, 
                                      const IProtocol* const pointProtocol) override;
        virtual IProtocol* createProtocol(EProtocolType type, OriginProtocol& originProtocol) override;
        virtual void destoryProtocol(EProtocolType type, IProtocol* protocol) override;
        virtual void setFlag(EFlag flag) override;
    };

    // Data type conversion functions
    namespace KNXDataTypes {
        bool data_to_bool(const uint8_t* data, uint8_t length);
        void bool_to_data(bool value, uint8_t* data, uint8_t& length);
        
        int8_t data_to_int8(const uint8_t* data, uint8_t length);
        void int8_to_data(int8_t value, uint8_t* data, uint8_t& length);
        
        uint8_t data_to_uint8(const uint8_t* data, uint8_t length);
        void uint8_to_data(uint8_t value, uint8_t* data, uint8_t& length);
        
        int16_t data_to_int16(const uint8_t* data, uint8_t length);
        void int16_to_data(int16_t value, uint8_t* data, uint8_t& length);
        
        uint16_t data_to_uint16(const uint8_t* data, uint8_t length);
        void uint16_to_data(uint16_t value, uint8_t* data, uint8_t& length);
        
        int32_t data_to_int32(const uint8_t* data, uint8_t length);
        void int32_to_data(int32_t value, uint8_t* data, uint8_t& length);
        
        uint32_t data_to_uint32(const uint8_t* data, uint8_t length);
        void uint32_to_data(uint32_t value, uint8_t* data, uint8_t& length);
        
        float data_to_float16(const uint8_t* data, uint8_t length);
        void float16_to_data(float value, uint8_t* data, uint8_t& length);
        
        float data_to_float32(const uint8_t* data, uint8_t length);
        void float32_to_data(float value, uint8_t* data, uint8_t& length);
    }
}

// Export functions for driver factory
extern "C" {
    DRIVER_API DRIVER::IDriver* createDriver();
    DRIVER_API void destoryDriver(DRIVER::IDriver* p);
}
